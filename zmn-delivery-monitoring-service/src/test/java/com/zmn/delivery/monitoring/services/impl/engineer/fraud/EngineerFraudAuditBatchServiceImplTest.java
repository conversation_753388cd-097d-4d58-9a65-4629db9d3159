package com.zmn.delivery.monitoring.services.impl.engineer.fraud;

import com.zmn.delivery.monitoring.common.enums.engineer.fraud.AuditBatchStatusEnum;
import com.zmn.delivery.monitoring.common.enums.engineer.fraud.FraudItemEnum;
import com.zmn.delivery.monitoring.model.entity.engineer.fraud.EngineerFraudAuditBatch;
import com.zmn.delivery.monitoring.model.query.engineer.fraud.EngineerFraudAuditBatchQuery;
import com.zmn.delivery.monitoring.persistence.interfaces.engineer.fraud.EngineerFraudAuditBatchDao;
import com.zmn.delivery.monitoring.services.base.BaseServiceIntegrationTest;
import com.zmn.delivery.monitoring.services.interfaces.engineer.fraud.EngineerFraudAuditBatchService;
import org.junit.jupiter.api.Test;

import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 工程师虚假稽查异常批次记录服务集成测试
 *
 * <AUTHOR>
 * @since 2025/07/10
 */
class EngineerFraudAuditBatchServiceImplTest extends BaseServiceIntegrationTest<EngineerFraudAuditBatchDao, EngineerFraudAuditBatchService> {

    @Override
    protected void setupTables(Statement statement) throws SQLException {
        statement.execute("""
                CREATE TABLE engineer_fraud_audit_batch(
                    `batch_id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '批次标识',
                    `behavior_id` INT(11) NOT NULL COMMENT '虚假行为标识',
                    `audit_item` SMALLINT(4) NOT NULL COMMENT '虚假稽查项;301吃跳单',
                    `engineer_id` INT(11) NOT NULL COMMENT '工程师标识',
                    `engineer_name` VARCHAR(255) NOT NULL COMMENT '工程师名称',
                    `batch_status` SMALLINT(4) NOT NULL COMMENT '批次稽查状态;20稽查完成 30处理完成',
                    `creater` VARCHAR(32) NOT NULL COMMENT '创建人',
                    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    `updater` VARCHAR(32) NOT NULL COMMENT '更新人',
                    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    PRIMARY KEY (batch_id)
                ) COMMENT = '工程师虚假稽查异常批次记录';
                """);
    }

    @Override
    protected EngineerFraudAuditBatchService setupService(EngineerFraudAuditBatchDao engineerFraudAuditBatchDao) {
        return new EngineerFraudAuditBatchServiceImpl(engineerFraudAuditBatchDao);
    }

    @Test
    void countByQuery() throws SQLException {
        statement.execute("""
                INSERT INTO engineer_fraud_audit_batch
                (behavior_id, audit_item, engineer_id, engineer_name, batch_status, creater, updater)
                VALUES
                (1, 103, 1001, '工程师A', 20, 'admin', 'admin');
                """);
        
        EngineerFraudAuditBatchQuery query = new EngineerFraudAuditBatchQuery();
        query.setBatchStatus(AuditBatchStatusEnum.AUDIT_COMPLETED);
        Integer count = service.countByQuery(query);
        assertEquals(1, count);
    }

    @Test
    void findByKey() throws SQLException {
        statement.execute("""
                INSERT INTO engineer_fraud_audit_batch
                (behavior_id, audit_item, engineer_id, engineer_name, batch_status, creater, updater)
                VALUES
                (1, 103, 1001, '工程师A', 20, 'admin', 'admin');
                """);

        EngineerFraudAuditBatch found = service.findByKey(1L);
        assertNotNull(found);
        assertEquals(1, found.getBehaviorId());
        assertEquals(FraudItemEnum.SKIP_SCAN_CODE, found.getAuditItem());
        assertEquals(1001, found.getEngineerId());
        assertEquals("工程师A", found.getEngineerName());
        assertEquals(AuditBatchStatusEnum.AUDIT_COMPLETED, found.getBatchStatus());
    }

    @Test
    void listByQuery() throws SQLException {
        statement.execute("""
                INSERT INTO engineer_fraud_audit_batch
                (behavior_id, audit_item, engineer_id, engineer_name, batch_status, creater, updater)
                VALUES
                (1, 103, 1001, '工程师A', 20, 'admin', 'admin'),
                (2, 103, 1002, '工程师B', 30, 'admin', 'admin');
                """);
        
        EngineerFraudAuditBatchQuery query = new EngineerFraudAuditBatchQuery();
        query.setBatchStatus(AuditBatchStatusEnum.AUDIT_COMPLETED);
        List<EngineerFraudAuditBatch> list = service.listByQuery(query);
        assertEquals(1, list.size());
        assertEquals("工程师A", list.get(0).getEngineerName());
    }

    @Test
    void updateByKey() throws SQLException {
        statement.execute("""
                INSERT INTO engineer_fraud_audit_batch
                (behavior_id, audit_item, engineer_id, engineer_name, batch_status, creater, updater)
                VALUES
                (1, 103, 1001, '工程师A', 20, 'admin', 'admin');
                """);
        
        EngineerFraudAuditBatch record = new EngineerFraudAuditBatch();
        record.setBatchId(1L);
        record.setBatchStatus(AuditBatchStatusEnum.PROCESS_COMPLETED);
        record.setUpdater("test");
        record.setUpdateTime(LocalDateTime.now());
        
        int result = service.updateByKey(record);
        assertEquals(1, result);
        
        EngineerFraudAuditBatch updated = service.findByKey(1L);
        assertEquals(AuditBatchStatusEnum.PROCESS_COMPLETED, updated.getBatchStatus());
    }

    @Test
    void insert() {
        EngineerFraudAuditBatch record = new EngineerFraudAuditBatch();
        record.setBehaviorId(1);
        record.setAuditItem(FraudItemEnum.SKIP_SCAN_CODE);
        record.setEngineerId(1001);
        record.setEngineerName("工程师A");
        record.setBatchStatus(AuditBatchStatusEnum.AUDIT_COMPLETED);
        record.setCreater("admin");
        record.setCreateTime(LocalDateTime.now());
        record.setUpdater("admin");
        record.setUpdateTime(LocalDateTime.now());
        
        int result = service.insert(record);
        assertEquals(1, result);
        assertNotNull(record.getBatchId());
        
        EngineerFraudAuditBatch found = service.findByKey(record.getBatchId());
        assertNotNull(found);
        assertEquals("工程师A", found.getEngineerName());
    }

    @Test
    void insertBatchOnDuplicateUpdate() {
        EngineerFraudAuditBatch record1 = new EngineerFraudAuditBatch();
        record1.setBehaviorId(1);
        record1.setAuditItem(FraudItemEnum.SKIP_SCAN_CODE);
        record1.setEngineerId(1001);
        record1.setEngineerName("工程师A");
        record1.setBatchStatus(AuditBatchStatusEnum.AUDIT_COMPLETED);
        record1.setCreater("admin");
        record1.setUpdater("admin");
        
        EngineerFraudAuditBatch record2 = new EngineerFraudAuditBatch();
        record2.setBehaviorId(2);
        record2.setAuditItem(FraudItemEnum.SKIP_SCAN_CODE);
        record2.setEngineerId(1002);
        record2.setEngineerName("工程师B");
        record2.setBatchStatus(AuditBatchStatusEnum.PROCESS_COMPLETED);
        record2.setCreater("admin");
        record2.setUpdater("admin");
        
        List<EngineerFraudAuditBatch> list = Arrays.asList(record1, record2);
        Integer result = service.insertBatchOnDuplicateUpdate(list);
        assertEquals(2, result);
        
        EngineerFraudAuditBatchQuery query = new EngineerFraudAuditBatchQuery();
        List<EngineerFraudAuditBatch> inserted = service.listByQuery(query);
        assertEquals(2, inserted.size());
    }
}
